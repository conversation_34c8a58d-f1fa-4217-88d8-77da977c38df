=== chat-me-now ===
Contributors: d<PERSON><PERSON><PERSON><PERSON> link: https://boliviahub.com/
Tags: comments, support, action button, whatsapp, chat, floating, start,chatting, whatsApp chat, webwhatsapp, wordpress chat. 
Stable tag: 1.0.2
Tested up to: 5.7.2
Requires PHP: 7.0
Requires at least: 4.6
License: GNUGPLv3
License URI: https://www.gnu.org/licenses/gpl.html

Floating button that opens the WhatsApp chat to the technical support on turn. It allows asign the work schedule up to 2 employees.

== Description ==
Integrate WhatsApp support dialog directly into your WordPress website and that will help you to increase leads/sales from your website. this is well design with lightwight code help to attract users for chat with active agent using What<PERSON><PERSON> application. 


= Available fields =
- whatsapp1
- whatsapp2
- whatsapp_active_turn
- icon_color
- background_color
- schedule_turn
- start_message
- active


= Usage =
Navigate to [the plugin documentation](https://boliviahub.com/chat-me-now) if you need more information on how to use this plugin.

= Support =
Find help in [our support plaftorm](https://boliviahub.com/chat-me-now/) for this plugin.

== Installation ==

= Installation =
1. In your WordPress admin panel, go to Plugins > New Plugin
2. Find our chat-me-now plugin by <PERSON> <PERSON> and click Install now
3. Alternatively, download the plugin and upload the contents of chat-me-now.zip to your plugins directory, which usually is /wp-content/plugins/
4. Activate the plugin
5. Enjoy

= Usage =
Go to [the plugin's documentation](https://boliviahub.com/chat-me-now/) for information on how to use it.

== Frequently Asked Questions ==
= No limitation for the Free version =

The free version of the chat-me-now is **not limited**.

= What if I need some speficial customization? = 

Just contact me and we can discuss about any new great feature.

= Usage =
Go to [the plugin's documentation](https://boliviahub.com/chat-me-now/) if you need more information on how to use this plugin.

= Support =
Find help on [our support plaftorm](https://boliviahub.com/chat-me-now/) for this plugin.

== Screenshots ==
1. Wide screen with the button
2. Medium screen with the button
3. Small screen with the button
4. Dashboard option screen

= 1.0.2 =
* version number fix

= 1.0.1 =
* start_message field with tag

= 1.0.0 =
* Initial release

== Changelog ==

= 1.0.2 =
* Added: start_message field
* Release date: 22 May 2021

= 1.0.1 =
* Release date: 21 May 2021

= 1.0.0 =
* Release date: 3 May 2021



== Upgrade Notice ==

= 1.0.2 =
The user can customize the start message
